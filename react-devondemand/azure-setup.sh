#!/bin/bash

# Azure Static Web Apps Setup and Verification Script
# This script helps diagnose and fix common Azure deployment issues

set -e

echo "🔍 Azure Static Web Apps Setup Verification"
echo "=========================================="

RESOURCE_GROUP="hyrr"
APP_NAME="devondemand"

# Check if Azure CLI is installed and user is logged in
if ! command -v az &> /dev/null; then
    echo "❌ Azure CLI is not installed. Please install it first:"
    echo "   https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
fi

if ! az account show &> /dev/null; then
    echo "❌ Not logged into Azure. Please run 'az login' first."
    exit 1
fi

echo "✅ Azure CLI is installed and you're logged in."

# Get current subscription info
SUBSCRIPTION=$(az account show --query "name" -o tsv)
echo "📋 Current subscription: $SUBSCRIPTION"

# Check if resource group exists
echo "🔍 Checking resource group '$RESOURCE_GROUP'..."
if az group show --name $RESOURCE_GROUP &> /dev/null; then
    echo "✅ Resource group '$RESOURCE_GROUP' exists."
else
    echo "❌ Resource group '$RESOURCE_GROUP' not found."
    echo "Available resource groups:"
    az group list --query "[].name" -o table
    exit 1
fi

# Check if Static Web App exists
echo "🔍 Checking Static Web App '$APP_NAME'..."
if az staticwebapp show --name $APP_NAME --resource-group $RESOURCE_GROUP &> /dev/null; then
    echo "✅ Static Web App '$APP_NAME' exists."
    
    # Get app details
    echo "📋 App details:"
    az staticwebapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query "{name:name,location:location,sku:sku.name,defaultHostname:defaultHostname}" -o table
    
    # Get deployment token
    echo "🔑 Getting deployment token..."
    DEPLOYMENT_TOKEN=$(az staticwebapp secrets list --name $APP_NAME --resource-group $RESOURCE_GROUP --query "properties.apiKey" -o tsv)
    
    if [ ! -z "$DEPLOYMENT_TOKEN" ]; then
        echo "✅ Deployment token retrieved successfully!"
        echo "🔐 Your deployment token (keep this secure):"
        echo "$DEPLOYMENT_TOKEN"
        echo ""
        echo "💡 To use this token for deployment:"
        echo "export AZURE_STATIC_WEB_APPS_API_TOKEN=\"$DEPLOYMENT_TOKEN\""
        echo "./deploy.sh"
        echo ""
        
        # Save token to .env file for convenience
        echo "AZURE_STATIC_WEB_APPS_API_TOKEN=$DEPLOYMENT_TOKEN" > .env
        echo "✅ Token saved to .env file for convenience."
    else
        echo "❌ Could not retrieve deployment token."
    fi
    
else
    echo "❌ Static Web App '$APP_NAME' not found in resource group '$RESOURCE_GROUP'."
    echo "Available Static Web Apps in this resource group:"
    az staticwebapp list --resource-group $RESOURCE_GROUP --query "[].name" -o table
    
    echo ""
    echo "🆕 To create a new Static Web App:"
    echo "az staticwebapp create \\"
    echo "  --name $APP_NAME \\"
    echo "  --resource-group $RESOURCE_GROUP \\"
    echo "  --location \"East US 2\" \\"
    echo "  --source https://github.com/yourusername/your-repo \\"
    echo "  --branch main \\"
    echo "  --app-location \"/\" \\"
    echo "  --output-location \"dist\""
fi

# Check Microsoft.Web provider registration
echo "🔍 Checking Microsoft.Web provider registration..."
PROVIDER_STATE=$(az provider show --namespace Microsoft.Web --query "registrationState" -o tsv)
if [ "$PROVIDER_STATE" = "Registered" ]; then
    echo "✅ Microsoft.Web provider is registered."
else
    echo "⚠️  Microsoft.Web provider is not registered. Registering now..."
    az provider register --namespace Microsoft.Web
    echo "✅ Microsoft.Web provider registration initiated. This may take a few minutes."
fi

# Check permissions (with error handling for graph database issues)
echo "🔍 Checking your permissions..."
USER_INFO=$(az account show --query "{name:user.name,type:user.type}" -o json)
USER_NAME=$(echo $USER_INFO | jq -r '.name')
USER_TYPE=$(echo $USER_INFO | jq -r '.type')

echo "📋 Current user: $USER_NAME (Type: $USER_TYPE)"

# Try to get role assignments, but handle the graph database error gracefully
if ROLE_ASSIGNMENTS=$(az role assignment list --assignee "$USER_NAME" --resource-group $RESOURCE_GROUP --query "[].roleDefinitionName" -o tsv 2>/dev/null); then
    echo "📋 Your roles in resource group '$RESOURCE_GROUP':"
    echo "$ROLE_ASSIGNMENTS"

    if echo "$ROLE_ASSIGNMENTS" | grep -q -E "(Owner|Contributor|Static Web App Contributor)"; then
        echo "✅ You have sufficient permissions for deployment."
    else
        echo "⚠️  You may not have sufficient permissions. Required roles:"
        echo "   - Owner, Contributor, or Static Web App Contributor"
    fi
else
    echo "⚠️  Cannot check role assignments due to Graph database issue."
    echo "This is common with external/guest accounts."
    echo "💡 Solutions:"
    echo "   1. Use deployment token method (bypasses permission checks)"
    echo "   2. Ask your Azure admin to add you as a proper user"
    echo "   3. Use a different Azure account with proper access"
    echo ""
    echo "🔑 The deployment token method should still work!"
fi

echo ""
echo "🎉 Setup verification complete!"
echo ""
echo "📝 Next steps:"
echo "1. If you have the deployment token, run: ./deploy.sh"
echo "2. Or set up GitHub Actions for automatic deployment"
echo "3. Check DEPLOYMENT_GUIDE.md for detailed instructions"
