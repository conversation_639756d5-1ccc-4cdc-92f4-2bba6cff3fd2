# DevOnDemand React App - Azure Static Web Apps Deployment Script (PowerShell)
# This script builds and deploys the React app to Azure Static Web Apps

$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting deployment to Azure Static Web Apps..." -ForegroundColor Green

# Check if required tools are installed
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "❌ npm is not installed. Please install Node.js and npm first." -ForegroundColor Red
    exit 1
}

if (-not (Get-Command az -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Azure CLI is not installed. Please install Azure CLI first." -ForegroundColor Red
    Write-Host "   Visit: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli" -ForegroundColor Yellow
    exit 1
}

# Check if logged into Azure
try {
    az account show | Out-Null
} catch {
    Write-Host "❌ Not logged into Azure. Please run 'az login' first." -ForegroundColor Red
    exit 1
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Blue
npm install

# Build the project
Write-Host "🔨 Building the project..." -ForegroundColor Blue
npm run build

# Check if build was successful
if (-not (Test-Path "dist")) {
    Write-Host "❌ Build failed. dist directory not found." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Build completed successfully!" -ForegroundColor Green

# Deploy using Azure CLI (if SWA CLI is not available)
Write-Host "🌐 Deploying to Azure Static Web Apps..." -ForegroundColor Blue

# Note: You'll need to replace these values with your actual Azure Static Web App details
$RESOURCE_GROUP = "your-resource-group"
$APP_NAME = "your-static-web-app-name"

# Check if Azure Static Web Apps CLI is installed
if (Get-Command swa -ErrorAction SilentlyContinue) {
    Write-Host "Using Azure Static Web Apps CLI..." -ForegroundColor Blue
    swa deploy ./dist --env production
} else {
    Write-Host "Azure Static Web Apps CLI not found. Using Azure CLI..." -ForegroundColor Yellow
    Write-Host "Please ensure you have created an Azure Static Web App resource first." -ForegroundColor Yellow
    Write-Host "Then update the RESOURCE_GROUP and APP_NAME variables in this script." -ForegroundColor Yellow
    
    Write-Host "⚠️  Manual deployment required:" -ForegroundColor Yellow
    Write-Host "   1. Go to Azure Portal" -ForegroundColor White
    Write-Host "   2. Navigate to your Static Web App" -ForegroundColor White
    Write-Host "   3. Upload the contents of the 'dist' folder" -ForegroundColor White
    Write-Host "   4. Or set up GitHub Actions using the workflow file in .github/workflows/" -ForegroundColor White
}

Write-Host "🎉 Deployment process completed!" -ForegroundColor Green
Write-Host "📁 Built files are in the 'dist' directory" -ForegroundColor Blue
Write-Host "🔗 Your app should be available at your Azure Static Web App URL" -ForegroundColor Blue
