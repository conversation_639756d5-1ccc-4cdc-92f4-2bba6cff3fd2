/** @license URI.js v4.4.1 (c) 2011 Gary Court. License: http://github.com/garycourt/uri-js */
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r(e.URI=e.URI||{})}(this,function(e){"use strict";function r(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];if(r.length>1){r[0]=r[0].slice(0,-1);for(var t=r.length-1,o=1;o<t;++o)r[o]=r[o].slice(1,-1);return r[t]=r[t].slice(1),r.join("")}return r[0]}function n(e){return"(?:"+e+")"}function t(e){return e===undefined?"undefined":null===e?"null":Object.prototype.toString.call(e).split(" ").pop().split("]").shift().toLowerCase()}function o(e){return e.toUpperCase()}function a(e){return e!==undefined&&null!==e?e instanceof Array?e:"number"!=typeof e.length||e.split||e.setInterval||e.call?[e]:Array.prototype.slice.call(e):[]}function i(e,r){var n=e;if(r)for(var t in r)n[t]=r[t];return n}function u(e){var t=r("[0-9]","[A-Fa-f]"),o=n(n("%[EFef]"+t+"%"+t+t+"%"+t+t)+"|"+n("%[89A-Fa-f]"+t+"%"+t+t)+"|"+n("%"+t+t)),a="[\\!\\$\\&\\'\\(\\)\\*\\+\\,\\;\\=]",i=r("[\\:\\/\\?\\#\\[\\]\\@]",a),u=e?"[\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]":"[]",s=e?"[\\uE000-\\uF8FF]":"[]",f=r("[A-Za-z]","[0-9]","[\\-\\.\\_\\~]",u),c=n(n("25[0-5]")+"|"+n("2[0-4][0-9]")+"|"+n("1[0-9][0-9]")+"|"+n("0?[1-9][0-9]")+"|0?0?[0-9]"),p=n(c+"\\."+c+"\\."+c+"\\."+c),h=n(t+"{1,4}"),d=n(n(h+"\\:"+h)+"|"+p),l=n(n(h+"\\:")+"{6}"+d),m=n("\\:\\:"+n(h+"\\:")+"{5}"+d),g=n(n(h)+"?\\:\\:"+n(h+"\\:")+"{4}"+d),v=n(n(n(h+"\\:")+"{0,1}"+h)+"?\\:\\:"+n(h+"\\:")+"{3}"+d),E=n(n(n(h+"\\:")+"{0,2}"+h)+"?\\:\\:"+n(h+"\\:")+"{2}"+d),C=n(n(n(h+"\\:")+"{0,3}"+h)+"?\\:\\:"+h+"\\:"+d),y=n(n(n(h+"\\:")+"{0,4}"+h)+"?\\:\\:"+d),S=n(n(n(h+"\\:")+"{0,5}"+h)+"?\\:\\:"+h),A=n(n(n(h+"\\:")+"{0,6}"+h)+"?\\:\\:"),D=n([l,m,g,v,E,C,y,S,A].join("|")),w=n(n(f+"|"+o)+"+");return{NOT_SCHEME:new RegExp(r("[^]","[A-Za-z]","[0-9]","[\\+\\-\\.]"),"g"),NOT_USERINFO:new RegExp(r("[^\\%\\:]",f,a),"g"),NOT_HOST:new RegExp(r("[^\\%\\[\\]\\:]",f,a),"g"),NOT_PATH:new RegExp(r("[^\\%\\/\\:\\@]",f,a),"g"),NOT_PATH_NOSCHEME:new RegExp(r("[^\\%\\/\\@]",f,a),"g"),NOT_QUERY:new RegExp(r("[^\\%]",f,a,"[\\:\\@\\/\\?]",s),"g"),NOT_FRAGMENT:new RegExp(r("[^\\%]",f,a,"[\\:\\@\\/\\?]"),"g"),ESCAPE:new RegExp(r("[^]",f,a),"g"),UNRESERVED:new RegExp(f,"g"),OTHER_CHARS:new RegExp(r("[^\\%]",f,i),"g"),PCT_ENCODED:new RegExp(o,"g"),IPV4ADDRESS:new RegExp("^("+p+")$"),IPV6ADDRESS:new RegExp("^\\[?("+D+")"+n(n("\\%25|\\%(?!"+t+"{2})")+"("+w+")")+"?\\]?$")}}function s(e){throw new RangeError(H[e])}function f(e,r){for(var n=[],t=e.length;t--;)n[t]=r(e[t]);return n}function c(e,r){var n=e.split("@"),t="";return n.length>1&&(t=n[0]+"@",e=n[1]),e=e.replace(j,"."),t+f(e.split("."),r).join(".")}function p(e){for(var r=[],n=0,t=e.length;n<t;){var o=e.charCodeAt(n++);if(o>=55296&&o<=56319&&n<t){var a=e.charCodeAt(n++);56320==(64512&a)?r.push(((1023&o)<<10)+(1023&a)+65536):(r.push(o),n--)}else r.push(o)}return r}function h(e){var r=e.charCodeAt(0);return r<16?"%0"+r.toString(16).toUpperCase():r<128?"%"+r.toString(16).toUpperCase():r<2048?"%"+(r>>6|192).toString(16).toUpperCase()+"%"+(63&r|128).toString(16).toUpperCase():"%"+(r>>12|224).toString(16).toUpperCase()+"%"+(r>>6&63|128).toString(16).toUpperCase()+"%"+(63&r|128).toString(16).toUpperCase()}function d(e){for(var r="",n=0,t=e.length;n<t;){var o=parseInt(e.substr(n+1,2),16);if(o<128)r+=String.fromCharCode(o),n+=3;else if(o>=194&&o<224){if(t-n>=6){var a=parseInt(e.substr(n+4,2),16);r+=String.fromCharCode((31&o)<<6|63&a)}else r+=e.substr(n,6);n+=6}else if(o>=224){if(t-n>=9){var i=parseInt(e.substr(n+4,2),16),u=parseInt(e.substr(n+7,2),16);r+=String.fromCharCode((15&o)<<12|(63&i)<<6|63&u)}else r+=e.substr(n,9);n+=9}else r+=e.substr(n,3),n+=3}return r}function l(e,r){function n(e){var n=d(e);return n.match(r.UNRESERVED)?n:e}return e.scheme&&(e.scheme=String(e.scheme).replace(r.PCT_ENCODED,n).toLowerCase().replace(r.NOT_SCHEME,"")),e.userinfo!==undefined&&(e.userinfo=String(e.userinfo).replace(r.PCT_ENCODED,n).replace(r.NOT_USERINFO,h).replace(r.PCT_ENCODED,o)),e.host!==undefined&&(e.host=String(e.host).replace(r.PCT_ENCODED,n).toLowerCase().replace(r.NOT_HOST,h).replace(r.PCT_ENCODED,o)),e.path!==undefined&&(e.path=String(e.path).replace(r.PCT_ENCODED,n).replace(e.scheme?r.NOT_PATH:r.NOT_PATH_NOSCHEME,h).replace(r.PCT_ENCODED,o)),e.query!==undefined&&(e.query=String(e.query).replace(r.PCT_ENCODED,n).replace(r.NOT_QUERY,h).replace(r.PCT_ENCODED,o)),e.fragment!==undefined&&(e.fragment=String(e.fragment).replace(r.PCT_ENCODED,n).replace(r.NOT_FRAGMENT,h).replace(r.PCT_ENCODED,o)),e}function m(e){return e.replace(/^0*(.*)/,"$1")||"0"}function g(e,r){var n=e.match(r.IPV4ADDRESS)||[],t=T(n,2),o=t[1];return o?o.split(".").map(m).join("."):e}function v(e,r){var n=e.match(r.IPV6ADDRESS)||[],t=T(n,3),o=t[1],a=t[2];if(o){for(var i=o.toLowerCase().split("::").reverse(),u=T(i,2),s=u[0],f=u[1],c=f?f.split(":").map(m):[],p=s.split(":").map(m),h=r.IPV4ADDRESS.test(p[p.length-1]),d=h?7:8,l=p.length-d,v=Array(d),E=0;E<d;++E)v[E]=c[E]||p[l+E]||"";h&&(v[d-1]=g(v[d-1],r));var C=v.reduce(function(e,r,n){if(!r||"0"===r){var t=e[e.length-1];t&&t.index+t.length===n?t.length++:e.push({index:n,length:1})}return e},[]),y=C.sort(function(e,r){return r.length-e.length})[0],S=void 0;if(y&&y.length>1){var A=v.slice(0,y.index),D=v.slice(y.index+y.length);S=A.join(":")+"::"+D.join(":")}else S=v.join(":");return a&&(S+="%"+a),S}return e}function E(e){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},n={},t=!1!==r.iri?R:F;"suffix"===r.reference&&(e=(r.scheme?r.scheme+":":"")+"//"+e);var o=e.match(K);if(o){W?(n.scheme=o[1],n.userinfo=o[3],n.host=o[4],n.port=parseInt(o[5],10),n.path=o[6]||"",n.query=o[7],n.fragment=o[8],isNaN(n.port)&&(n.port=o[5])):(n.scheme=o[1]||undefined,n.userinfo=-1!==e.indexOf("@")?o[3]:undefined,n.host=-1!==e.indexOf("//")?o[4]:undefined,n.port=parseInt(o[5],10),n.path=o[6]||"",n.query=-1!==e.indexOf("?")?o[7]:undefined,n.fragment=-1!==e.indexOf("#")?o[8]:undefined,isNaN(n.port)&&(n.port=e.match(/\/\/(?:.|\n)*\:(?:\/|\?|\#|$)/)?o[4]:undefined)),n.host&&(n.host=v(g(n.host,t),t)),n.scheme!==undefined||n.userinfo!==undefined||n.host!==undefined||n.port!==undefined||n.path||n.query!==undefined?n.scheme===undefined?n.reference="relative":n.fragment===undefined?n.reference="absolute":n.reference="uri":n.reference="same-document",r.reference&&"suffix"!==r.reference&&r.reference!==n.reference&&(n.error=n.error||"URI is not a "+r.reference+" reference.");var a=J[(r.scheme||n.scheme||"").toLowerCase()];if(r.unicodeSupport||a&&a.unicodeSupport)l(n,t);else{if(n.host&&(r.domainHost||a&&a.domainHost))try{n.host=B.toASCII(n.host.replace(t.PCT_ENCODED,d).toLowerCase())}catch(i){n.error=n.error||"Host's domain name can not be converted to ASCII via punycode: "+i}l(n,F)}a&&a.parse&&a.parse(n,r)}else n.error=n.error||"URI can not be parsed.";return n}function C(e,r){var n=!1!==r.iri?R:F,t=[];return e.userinfo!==undefined&&(t.push(e.userinfo),t.push("@")),e.host!==undefined&&t.push(v(g(String(e.host),n),n).replace(n.IPV6ADDRESS,function(e,r,n){return"["+r+(n?"%25"+n:"")+"]"})),"number"!=typeof e.port&&"string"!=typeof e.port||(t.push(":"),t.push(String(e.port))),t.length?t.join(""):undefined}function y(e){for(var r=[];e.length;)if(e.match(X))e=e.replace(X,"");else if(e.match(ee))e=e.replace(ee,"/");else if(e.match(re))e=e.replace(re,"/"),r.pop();else if("."===e||".."===e)e="";else{var n=e.match(ne);if(!n)throw new Error("Unexpected dot segment condition");var t=n[0];e=e.slice(t.length),r.push(t)}return r.join("")}function S(e){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},n=r.iri?R:F,t=[],o=J[(r.scheme||e.scheme||"").toLowerCase()];if(o&&o.serialize&&o.serialize(e,r),e.host)if(n.IPV6ADDRESS.test(e.host));else if(r.domainHost||o&&o.domainHost)try{e.host=r.iri?B.toUnicode(e.host):B.toASCII(e.host.replace(n.PCT_ENCODED,d).toLowerCase())}catch(u){e.error=e.error||"Host's domain name can not be converted to "+(r.iri?"Unicode":"ASCII")+" via punycode: "+u}l(e,n),"suffix"!==r.reference&&e.scheme&&(t.push(e.scheme),t.push(":"));var a=C(e,r);if(a!==undefined&&("suffix"!==r.reference&&t.push("//"),t.push(a),e.path&&"/"!==e.path.charAt(0)&&t.push("/")),e.path!==undefined){var i=e.path;r.absolutePath||o&&o.absolutePath||(i=y(i)),a===undefined&&(i=i.replace(/^\/\//,"/%2F")),t.push(i)}return e.query!==undefined&&(t.push("?"),t.push(e.query)),e.fragment!==undefined&&(t.push("#"),t.push(e.fragment)),t.join("")}function A(e,r){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{},t=arguments[3],o={};return t||(e=E(S(e,n),n),r=E(S(r,n),n)),n=n||{},!n.tolerant&&r.scheme?(o.scheme=r.scheme,o.userinfo=r.userinfo,o.host=r.host,o.port=r.port,o.path=y(r.path||""),o.query=r.query):(r.userinfo!==undefined||r.host!==undefined||r.port!==undefined?(o.userinfo=r.userinfo,o.host=r.host,o.port=r.port,o.path=y(r.path||""),o.query=r.query):(r.path?("/"===r.path.charAt(0)?o.path=y(r.path):(e.userinfo===undefined&&e.host===undefined&&e.port===undefined||e.path?e.path?o.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+r.path:o.path=r.path:o.path="/"+r.path,o.path=y(o.path)),o.query=r.query):(o.path=e.path,r.query!==undefined?o.query=r.query:o.query=e.query),o.userinfo=e.userinfo,o.host=e.host,o.port=e.port),o.scheme=e.scheme),o.fragment=r.fragment,o}function D(e,r,n){var t=i({scheme:"null"},n);return S(A(E(e,t),E(r,t),t,!0),t)}function w(e,r){return"string"==typeof e?e=S(E(e,r),r):"object"===t(e)&&(e=E(S(e,r),r)),e}function b(e,r,n){return"string"==typeof e?e=S(E(e,n),n):"object"===t(e)&&(e=S(e,n)),"string"==typeof r?r=S(E(r,n),n):"object"===t(r)&&(r=S(r,n)),e===r}function x(e,r){return e&&e.toString().replace(r&&r.iri?R.ESCAPE:F.ESCAPE,h)}function O(e,r){return e&&e.toString().replace(r&&r.iri?R.PCT_ENCODED:F.PCT_ENCODED,d)}function N(e){return"boolean"==typeof e.secure?e.secure:"wss"===String(e.scheme).toLowerCase()}function I(e){var r=d(e);return r.match(he)?r:e}var F=u(!1),R=u(!0),T=function(){function e(e,r){var n=[],t=!0,o=!1,a=undefined;try{for(var i,u=e[Symbol.iterator]();!(t=(i=u.next()).done)&&(n.push(i.value),!r||n.length!==r);t=!0);}catch(s){o=!0,a=s}finally{try{!t&&u["return"]&&u["return"]()}finally{if(o)throw a}}return n}return function(r,n){if(Array.isArray(r))return r;if(Symbol.iterator in Object(r))return e(r,n);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),_=function(e){if(Array.isArray(e)){for(var r=0,n=Array(e.length);r<e.length;r++)n[r]=e[r];return n}return Array.from(e)},P=2147483647,q=/^xn--/,U=/[^\0-\x7E]/,j=/[\x2E\u3002\uFF0E\uFF61]/g,H={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},z=Math.floor,L=String.fromCharCode,$=function(e){return String.fromCodePoint.apply(String,_(e))},M=function(e){return e-48<10?e-22:e-65<26?e-65:e-97<26?e-97:36},V=function(e,r){return e+22+75*(e<26)-((0!=r)<<5)},k=function(e,r,n){var t=0;for(e=n?z(e/700):e>>1,e+=z(e/r);e>455;t+=36)e=z(e/35);return z(t+36*e/(e+38))},Z=function(e){var r=[],n=e.length,t=0,o=128,a=72,i=e.lastIndexOf("-");i<0&&(i=0);for(var u=0;u<i;++u)e.charCodeAt(u)>=128&&s("not-basic"),r.push(e.charCodeAt(u));for(var f=i>0?i+1:0;f<n;){for(var c=t,p=1,h=36;;h+=36){f>=n&&s("invalid-input");var d=M(e.charCodeAt(f++));(d>=36||d>z((P-t)/p))&&s("overflow"),t+=d*p;var l=h<=a?1:h>=a+26?26:h-a;if(d<l)break;var m=36-l;p>z(P/m)&&s("overflow"),p*=m}var g=r.length+1;a=k(t-c,g,0==c),z(t/g)>P-o&&s("overflow"),o+=z(t/g),t%=g,r.splice(t++,0,o)}return String.fromCodePoint.apply(String,r)},G=function(e){var r=[];e=p(e);var n=e.length,t=128,o=0,a=72,i=!0,u=!1,f=undefined;try{for(var c,h=e[Symbol.iterator]();!(i=(c=h.next()).done);i=!0){var d=c.value;d<128&&r.push(L(d))}}catch(U){u=!0,f=U}finally{try{!i&&h["return"]&&h["return"]()}finally{if(u)throw f}}var l=r.length,m=l;for(l&&r.push("-");m<n;){var g=P,v=!0,E=!1,C=undefined;try{for(var y,S=e[Symbol.iterator]();!(v=(y=S.next()).done);v=!0){var A=y.value;A>=t&&A<g&&(g=A)}}catch(U){E=!0,C=U}finally{try{!v&&S["return"]&&S["return"]()}finally{if(E)throw C}}var D=m+1;g-t>z((P-o)/D)&&s("overflow"),o+=(g-t)*D,t=g;var w=!0,b=!1,x=undefined;try{for(var O,N=e[Symbol.iterator]();!(w=(O=N.next()).done);w=!0){var I=O.value;if(I<t&&++o>P&&s("overflow"),I==t){for(var F=o,R=36;;R+=36){var T=R<=a?1:R>=a+26?26:R-a;if(F<T)break;var _=F-T,q=36-T;r.push(L(V(T+_%q,0))),F=z(_/q)}r.push(L(V(F,0))),a=k(o,D,m==l),o=0,++m}}}catch(U){b=!0,x=U}finally{try{!w&&N["return"]&&N["return"]()}finally{if(b)throw x}}++o,++t}return r.join("")},Q=function(e){return c(e,function(e){return q.test(e)?Z(e.slice(4).toLowerCase()):e})},Y=function(e){return c(e,function(e){return U.test(e)?"xn--"+G(e):e})},B={version:"2.1.0",ucs2:{decode:p,encode:$},decode:Z,encode:G,toASCII:Y,toUnicode:Q},J={},K=/^(?:([^:\/?#]+):)?(?:\/\/((?:([^\/?#@]*)@)?(\[[^\/?#\]]+\]|[^\/?#:]*)(?:\:(\d*))?))?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n|\r)*))?/i,W="".match(/(){0}/)[1]===undefined,X=/^\.\.?\//,ee=/^\/\.(\/|$)/,re=/^\/\.\.(\/|$)/,ne=/^\/?(?:.|\n)*?(?=\/|$)/,te={scheme:"http",domainHost:!0,parse:function(e,r){return e.host||(e.error=e.error||"HTTP URIs must have a host."),e},serialize:function(e,r){var n="https"===String(e.scheme).toLowerCase();return e.port!==(n?443:80)&&""!==e.port||(e.port=undefined),e.path||(e.path="/"),e}},oe={scheme:"https",domainHost:te.domainHost,parse:te.parse,serialize:te.serialize},ae={scheme:"ws",domainHost:!0,parse:function(e,r){var n=e;return n.secure=N(n),n.resourceName=(n.path||"/")+(n.query?"?"+n.query:""),n.path=undefined,n.query=undefined,n},serialize:function(e,r){if(e.port!==(N(e)?443:80)&&""!==e.port||(e.port=undefined),"boolean"==typeof e.secure&&(e.scheme=e.secure?"wss":"ws",e.secure=undefined),e.resourceName){var n=e.resourceName.split("?"),t=T(n,2),o=t[0],a=t[1];e.path=o&&"/"!==o?o:undefined,e.query=a,e.resourceName=undefined}return e.fragment=undefined,e}},ie={scheme:"wss",domainHost:ae.domainHost,parse:ae.parse,serialize:ae.serialize},ue={},se="[A-Za-z0-9\\-\\.\\_\\~\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]",fe="[0-9A-Fa-f]",ce=n(n("%[EFef][0-9A-Fa-f]%"+fe+fe+"%"+fe+fe)+"|"+n("%[89A-Fa-f][0-9A-Fa-f]%"+fe+fe)+"|"+n("%"+fe+fe)),pe=r("[\\!\\$\\%\\'\\(\\)\\*\\+\\,\\-\\.0-9\\<\\>A-Z\\x5E-\\x7E]",'[\\"\\\\]'),he=new RegExp(se,"g"),de=new RegExp(ce,"g"),le=new RegExp(r("[^]","[A-Za-z0-9\\!\\$\\%\\'\\*\\+\\-\\^\\_\\`\\{\\|\\}\\~]","[\\.]",'[\\"]',pe),"g"),me=new RegExp(r("[^]",se,"[\\!\\$\\'\\(\\)\\*\\+\\,\\;\\:\\@]"),"g"),ge=me,ve={scheme:"mailto",parse:function(e,r){var n=e,t=n.to=n.path?n.path.split(","):[];if(n.path=undefined,n.query){for(var o=!1,a={},i=n.query.split("&"),u=0,s=i.length;u<s;++u){var f=i[u].split("=");switch(f[0]){case"to":for(var c=f[1].split(","),p=0,h=c.length;p<h;++p)t.push(c[p]);break;case"subject":n.subject=O(f[1],r);break;case"body":n.body=O(f[1],r);break;default:o=!0,a[O(f[0],r)]=O(f[1],r)}}o&&(n.headers=a)}n.query=undefined;for(var d=0,l=t.length;d<l;++d){var m=t[d].split("@");if(m[0]=O(m[0]),r.unicodeSupport)m[1]=O(m[1],r).toLowerCase();else try{m[1]=B.toASCII(O(m[1],r).toLowerCase())}catch(g){n.error=n.error||"Email address's domain name can not be converted to ASCII via punycode: "+g}t[d]=m.join("@")}return n},serialize:function(e,r){var n=e,t=a(e.to);if(t){for(var i=0,u=t.length;i<u;++i){var s=String(t[i]),f=s.lastIndexOf("@"),c=s.slice(0,f).replace(de,I).replace(de,o).replace(le,h),p=s.slice(f+1);try{p=r.iri?B.toUnicode(p):B.toASCII(O(p,r).toLowerCase())}catch(g){n.error=n.error||"Email address's domain name can not be converted to "+(r.iri?"Unicode":"ASCII")+" via punycode: "+g}t[i]=c+"@"+p}n.path=t.join(",")}var d=e.headers=e.headers||{};e.subject&&(d.subject=e.subject),e.body&&(d.body=e.body);var l=[];for(var m in d)d[m]!==ue[m]&&l.push(m.replace(de,I).replace(de,o).replace(me,h)+"="+d[m].replace(de,I).replace(de,o).replace(ge,h));return l.length&&(n.query=l.join("&")),n}},Ee=/^([^\:]+)\:(.*)/,Ce={scheme:"urn",parse:function(e,r){var n=e.path&&e.path.match(Ee),t=e;if(n){var o=r.scheme||t.scheme||"urn",a=n[1].toLowerCase(),i=n[2],u=o+":"+(r.nid||a),s=J[u];t.nid=a,t.nss=i,t.path=undefined,s&&(t=s.parse(t,r))}else t.error=t.error||"URN can not be parsed.";return t},serialize:function(e,r){var n=r.scheme||e.scheme||"urn",t=e.nid,o=n+":"+(r.nid||t),a=J[o];a&&(e=a.serialize(e,r));var i=e,u=e.nss;return i.path=(t||r.nid)+":"+u,i}},ye=/^[0-9A-Fa-f]{8}(?:\-[0-9A-Fa-f]{4}){3}\-[0-9A-Fa-f]{12}$/,Se={scheme:"urn:uuid",parse:function(e,r){var n=e;return n.uuid=n.nss,n.nss=undefined,r.tolerant||n.uuid&&n.uuid.match(ye)||(n.error=n.error||"UUID is not valid."),n},serialize:function(e,r){var n=e;return n.nss=(e.uuid||"").toLowerCase(),n}};J[te.scheme]=te,J[oe.scheme]=oe,J[ae.scheme]=ae,J[ie.scheme]=ie,J[ve.scheme]=ve,J[Ce.scheme]=Ce,J[Se.scheme]=Se,e.SCHEMES=J,e.pctEncChar=h,e.pctDecChars=d,e.parse=E,e.removeDotSegments=y,e.serialize=S,e.resolveComponents=A,e.resolve=D,e.normalize=w,e.equal=b,e.escapeComponent=x,e.unescapeComponent=O,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=uri.all.min.js.map