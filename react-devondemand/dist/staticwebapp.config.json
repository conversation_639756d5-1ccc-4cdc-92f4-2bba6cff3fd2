{"routes": [{"route": "/assets/*", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}], "navigationFallback": {"rewrite": "/index.html", "exclude": ["/assets/*", "/images/*.{png,jpg,gif}", "/css/*", "*.js", "*.css", "*.svg", "*.ico"]}, "mimeTypes": {".js": "application/javascript", ".css": "text/css", ".json": "application/json", ".svg": "image/svg+xml"}, "globalHeaders": {"Cache-Control": "no-cache"}, "responseOverrides": {"404": {"rewrite": "/index.html", "statusCode": 200}}}