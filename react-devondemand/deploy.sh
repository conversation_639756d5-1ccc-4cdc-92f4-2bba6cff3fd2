#!/bin/bash

# DevOnDemand React App - Azure Static Web Apps Deployment Script
# This script builds and deploys the React app to Azure Static Web Apps

set -e  # Exit on any error

echo "🚀 Starting deployment to Azure Static Web Apps..."

# Check if required tools are installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install Node.js and npm first."
    exit 1
fi

if ! command -v az &> /dev/null; then
    echo "❌ Azure CLI is not installed. Please install Azure CLI first."
    echo "   Visit: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
fi

# Check if logged into Azure
if ! az account show &> /dev/null; then
    echo "❌ Not logged into Azure. Please run 'az login' first."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the project
echo "🔨 Building the project..."
npm run build

# Check if build was successful
if [ ! -d "dist" ]; then
    echo "❌ Build failed. dist directory not found."
    exit 1
fi

echo "✅ Build completed successfully!"

# Deploy using Azure CLI (if SWA CLI is not available)
echo "🌐 Deploying to Azure Static Web Apps..."

# Note: You'll need to replace these values with your actual Azure Static Web App details
RESOURCE_GROUP="hyrr"
APP_NAME="devondemand"

# Check if Azure Static Web Apps CLI is installed
if command -v swa &> /dev/null; then
    echo "Using Azure Static Web Apps CLI..."

    # Try different deployment approaches
    echo "Attempting deployment with SWA CLI..."

    # Method 1: Try with resource group and app name
    if swa deploy ./dist --resource-group $RESOURCE_GROUP --app-name $APP_NAME --env production; then
        echo "✅ Deployment successful with resource group method!"
    else
        echo "⚠️  Resource group method failed, trying alternative..."

        # Method 2: Try with deployment token (if available)
        if [ ! -z "$AZURE_STATIC_WEB_APPS_API_TOKEN" ]; then
            echo "Using deployment token..."
            swa deploy ./dist --deployment-token $AZURE_STATIC_WEB_APPS_API_TOKEN
        else
            echo "❌ No deployment token found. Please set AZURE_STATIC_WEB_APPS_API_TOKEN environment variable."
            echo "You can find this token in your Azure Static Web App's 'Manage deployment token' section."

            # Method 3: Interactive deployment
            echo "Trying interactive deployment..."
            swa deploy ./dist --env production
        fi
    fi
else
    echo "Azure Static Web Apps CLI not found. Installing..."
    npm install -g @azure/static-web-apps-cli

    if command -v swa &> /dev/null; then
        echo "SWA CLI installed successfully. Retrying deployment..."
        swa deploy ./dist --resource-group $RESOURCE_GROUP --app-name $APP_NAME --env production
    else
        echo "❌ Failed to install SWA CLI. Using manual deployment instructions..."
        echo "⚠️  Manual deployment required:"
        echo "   1. Go to Azure Portal (https://portal.azure.com)"
        echo "   2. Navigate to your Static Web App: $APP_NAME"
        echo "   3. Go to 'Overview' and click 'Browse' to see current site"
        echo "   4. Use GitHub Actions for automatic deployment (recommended)"
        echo "   5. Or manually upload the contents of the 'dist' folder"
    fi
fi

echo "🎉 Deployment process completed!"
echo "📁 Built files are in the 'dist' directory"
echo "🔗 Your app should be available at your Azure Static Web App URL"
