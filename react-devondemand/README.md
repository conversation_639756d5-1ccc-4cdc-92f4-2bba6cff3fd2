# DevOnDemand - React App

A modern React application for on-demand developer services, built with Vite and deployed to Azure Static Web Apps.

## 🚀 Features

- **Modern React**: Built with React 18 and Vite for fast development
- **Responsive Design**: Mobile-first responsive design with modern CSS
- **Contact Form**: Integrated contact form with API submission
- **Smooth Scrolling**: Smooth navigation between sections
- **Azure Deployment**: Ready for deployment to Azure Static Web Apps

## 🛠️ Tech Stack

- **Frontend**: React 18, Vite
- **Styling**: CSS3 with modern features (Grid, Flexbox, Gradients)
- **Icons**: Font Awesome 6
- **Fonts**: Inter font family from Google Fonts
- **Deployment**: Azure Static Web Apps with GitHub Actions

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd react-devondemand
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## 🏗️ Build

To build the project for production:

```bash
npm run build
```

The built files will be in the `dist` directory.

## 🌐 Deployment to Azure Static Web Apps

### Option 1: Automatic Deployment with GitHub Actions (Recommended)

1. **Create an Azure Static Web App**:
   - Go to [Azure Portal](https://portal.azure.com)
   - Create a new "Static Web App" resource
   - Connect it to your GitHub repository
   - Set the build configuration:
     - App location: `/`
     - Output location: `dist`

2. **Configure GitHub Secrets**:
   - Azure will automatically add the `AZURE_STATIC_WEB_APPS_API_TOKEN` secret to your repository
   - The GitHub Actions workflow in `.github/workflows/azure-static-web-apps.yml` will handle automatic deployments

3. **Push to main branch**:
   ```bash
   git push origin main
   ```

   The app will automatically build and deploy!

### Option 2: Manual Deployment with Scripts

#### Prerequisites
- [Azure CLI](https://docs.microsoft.com/en-us/cli/azure/install-azure-cli) installed
- [Azure Static Web Apps CLI](https://azure.github.io/static-web-apps-cli/) (optional but recommended)
- Logged into Azure: `az login`

#### For Linux/macOS:
```bash
./deploy.sh
```

#### For Windows (PowerShell):
```powershell
.\deploy.ps1
```

### Option 3: Azure Static Web Apps CLI

1. Install the SWA CLI:
```bash
npm install -g @azure/static-web-apps-cli
```

2. Build the project:
```bash
npm run build
```

3. Deploy:
```bash
swa deploy ./dist --env production
```

## 📁 Project Structure

```
react-devondemand/
├── .github/
│   └── workflows/
│       └── azure-static-web-apps.yml    # GitHub Actions workflow
├── public/
│   └── staticwebapp.config.json         # Azure SWA configuration
├── src/
│   ├── components/
│   │   ├── Header.jsx                   # Navigation header
│   │   ├── Hero.jsx                     # Hero section
│   │   ├── Services.jsx                 # Services section
│   │   ├── Contact.jsx                  # Contact form
│   │   └── Footer.jsx                   # Footer
│   ├── App.jsx                          # Main app component
│   ├── main.jsx                         # App entry point
│   └── index.css                        # Global styles
├── deploy.sh                            # Linux/macOS deployment script
├── deploy.ps1                           # Windows deployment script
└── README.md
```

## 🎨 Customization

### Styling
- Global styles are in `src/index.css`
- The design uses CSS custom properties for easy theming
- Responsive breakpoints: 768px (tablet) and 480px (mobile)

### Components
- All components are in `src/components/`
- Each component is self-contained and reusable
- Form handling is done with React hooks

### API Integration
- Contact form submits to: `https://techyrr-server.gentlebay-8bbb7147.centralindia.azurecontainerapps.io/contact-us`
- Update the API endpoint in `src/components/Contact.jsx` if needed

## 🔧 Configuration

### Azure Static Web Apps Configuration
The `public/staticwebapp.config.json` file contains:
- Routing rules for SPA behavior
- MIME type configurations
- Global headers
- 404 fallback to index.html

### Build Configuration
- Vite configuration is in `vite.config.js`
- Build output goes to `dist/` directory
- Supports modern ES modules and legacy browser fallbacks

## 🚨 Troubleshooting

### Common Issues

1. **Build fails**: Check Node.js version (requires Node 16+)
2. **Icons not showing**: Ensure Font Awesome CDN is accessible
3. **Form not submitting**: Check API endpoint and CORS settings
4. **Deployment fails**: Verify Azure CLI login and permissions

### Development Issues

1. **Hot reload not working**: Restart the dev server
2. **Styles not updating**: Clear browser cache
3. **Components not rendering**: Check console for JavaScript errors

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support with this application, please contact the development team or create an issue in the repository.
