#!/bin/bash

# Azure Authentication Fix Script
# This script helps resolve common Azure CLI authentication issues

echo "🔧 Azure Authentication Fix"
echo "=========================="

echo "Current Azure account info:"
az account show --query "{name:user.name,type:user.type,tenantId:tenantId,subscriptionName:name}" -o table

echo ""
echo "🚨 Detected Issue: Graph database error for user '<EMAIL>'"
echo ""
echo "This error occurs when:"
echo "1. You're using an external/guest account"
echo "2. Your account isn't properly registered in Azure AD"
echo "3. There are permission/authentication issues"
echo ""

echo "🔧 **SOLUTION OPTIONS:**"
echo ""

echo "**Option 1: Re-authenticate with proper tenant (RECOMMENDED)**"
echo "1. Clear current authentication:"
echo "   az logout"
echo "   az login --tenant YOUR_TENANT_ID"
echo ""
echo "2. If you don't know your tenant ID, try:"
echo "   az login"
echo "   # Then select the correct tenant when prompted"
echo ""

echo "**Option 2: Use Deployment Token (BYPASS AUTHENTICATION)**"
echo "This is the most reliable method for your situation:"
echo ""
echo "1. Go to Azure Portal: https://portal.azure.com"
echo "2. Navigate to: Resource Groups → hyrr → devondemand (Static Web App)"
echo "3. Click 'Manage deployment token'"
echo "4. Copy the token"
echo "5. Run: export AZURE_STATIC_WEB_APPS_API_TOKEN=\"your-token-here\""
echo "6. Run: ./deploy.sh"
echo ""

echo "**Option 3: Use GitHub Actions (BEST LONG-TERM SOLUTION)**"
echo "1. Push your code to GitHub"
echo "2. In Azure Portal, create a new Static Web App"
echo "3. Connect it to your GitHub repository"
echo "4. Azure will automatically set up deployment"
echo ""

echo "**Option 4: Manual Portal Upload**"
echo "1. Run: npm run build"
echo "2. Go to Azure Portal → your Static Web App"
echo "3. Use the 'Browse' feature to access deployment center"
echo "4. Upload the 'dist' folder contents"
echo ""

echo "🎯 **IMMEDIATE ACTION RECOMMENDED:**"
echo ""
echo "Since you have authentication issues, use the deployment token method:"
echo ""
echo "1. Get token from Azure Portal (Static Web App → Manage deployment token)"
echo "2. Run these commands:"
echo "   export AZURE_STATIC_WEB_APPS_API_TOKEN=\"your-token\""
echo "   npm run build"
echo "   npx @azure/static-web-apps-cli deploy ./dist --deployment-token \$AZURE_STATIC_WEB_APPS_API_TOKEN"
echo ""

echo "This bypasses all authentication and permission issues!"
echo ""

# Try to get the deployment token anyway (might work)
echo "🔍 Attempting to get deployment token despite auth issues..."
if TOKEN=$(az staticwebapp secrets list --name devondemand --resource-group hyrr --query "properties.apiKey" -o tsv 2>/dev/null); then
    echo "✅ Successfully retrieved deployment token!"
    echo "🔐 Your deployment token:"
    echo "$TOKEN"
    echo ""
    echo "💾 Saving to .env file..."
    echo "AZURE_STATIC_WEB_APPS_API_TOKEN=$TOKEN" > .env
    echo "✅ Token saved! Now run: ./deploy.sh"
else
    echo "❌ Cannot retrieve token via CLI due to authentication issues."
    echo "Please get the token manually from Azure Portal."
fi

echo ""
echo "📞 Need more help?"
echo "1. Check Azure Portal directly"
echo "2. Contact your Azure administrator"
echo "3. Use GitHub Actions for automated deployment"
