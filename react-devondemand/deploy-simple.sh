#!/bin/bash

# Simple Azure Static Web Apps Deployment
# This script uses only the deployment token, bypassing all authentication issues

set -e

echo "🚀 Simple Azure Static Web Apps Deployment"
echo "=========================================="

# Build the project
echo "🔨 Building the project..."
npm run build

# Check if build was successful
if [ ! -d "dist" ]; then
    echo "❌ Build failed. dist directory not found."
    exit 1
fi

echo "✅ Build completed successfully!"

# Check for deployment token
if [ -z "$AZURE_STATIC_WEB_APPS_API_TOKEN" ]; then
    # Try to load from .env file
    if [ -f ".env" ]; then
        echo "📄 Loading token from .env file..."
        export $(cat .env | xargs)
    fi
    
    if [ -z "$AZURE_STATIC_WEB_APPS_API_TOKEN" ]; then
        echo "❌ No deployment token found!"
        echo ""
        echo "🔑 To get your deployment token:"
        echo "1. Go to https://portal.azure.com"
        echo "2. Navigate to: Resource Groups → hyrr → devondemand"
        echo "3. Click 'Manage deployment token'"
        echo "4. Copy the token"
        echo "5. Run: export AZURE_STATIC_WEB_APPS_API_TOKEN=\"your-token-here\""
        echo "6. Then run this script again"
        echo ""
        echo "Or create a .env file with:"
        echo "AZURE_STATIC_WEB_APPS_API_TOKEN=your-token-here"
        exit 1
    fi
fi

echo "✅ Deployment token found!"

# Install SWA CLI if not available
if ! command -v swa &> /dev/null; then
    echo "📦 Installing Azure Static Web Apps CLI..."
    npm install -g @azure/static-web-apps-cli
fi

# Deploy using the token
echo "🌐 Deploying to Azure Static Web Apps..."
echo "Using deployment token authentication..."

if swa deploy ./dist --deployment-token "$AZURE_STATIC_WEB_APPS_API_TOKEN"; then
    echo ""
    echo "🎉 Deployment successful!"
    echo "🔗 Your app should be live at: https://devondemand.azurestaticapps.net"
    echo ""
    echo "✅ Next steps:"
    echo "1. Visit your app URL to verify deployment"
    echo "2. Test all functionality (navigation, contact form, etc.)"
    echo "3. Set up GitHub Actions for automatic future deployments"
else
    echo ""
    echo "❌ Deployment failed!"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "1. Verify your deployment token is correct"
    echo "2. Check if your Static Web App exists in Azure Portal"
    echo "3. Ensure you have the correct permissions"
    echo "4. Try the GitHub Actions deployment method instead"
fi
