import React from 'react';

const Services = () => {
  const features = [
    {
      icon: 'fas fa-dollar-sign',
      title: 'Simple, Task-Based Pricing',
      description: 'Forget hourly confusion. You pay only for what gets done.',
      points: [
        'No long-term commitment',
        'No surprises'
      ]
    },
    {
      icon: 'fas fa-user-tie',
      title: 'Project Manager Included',
      description: 'Every task is backed by an expert Product Manager who:',
      points: [
        'Helps scope the task clearly',
        'Communicates between you and developers',
        'Ensures timelines and quality'
      ]
    },
    {
      icon: 'fas fa-lightning-bolt',
      title: 'Focus on What Matters',
      description: 'You focus on what needs to be done—we handle how it gets done.',
      points: [
        'Expert developers',
        'Quality assurance'
      ]
    }
  ];

  return (
    <section id="services" className="services">
      <div className="container">
        <div className="section-header">
          <h2>One Line of Code or a Whole Module—We've Got You</h2>
          <p>
            We provide on-demand developers for well-scoped tasks. Whether it's fixing a bug, 
            integrating an API, or building a full feature, we deliver with speed and precision.
          </p>
        </div>

        <div className="features-grid">
          {features.map((feature, index) => (
            <div key={index} className="feature-card">
              <div className="feature-icon">
                <i className={feature.icon}></i>
              </div>
              <h3>{feature.title}</h3>
              <p>{feature.description}</p>
              <ul className="feature-list">
                {feature.points.map((point, pointIndex) => (
                  <li key={pointIndex}>
                    <i className="fas fa-check"></i>
                    {point}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
