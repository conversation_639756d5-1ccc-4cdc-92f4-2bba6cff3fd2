import React, { useState } from 'react';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    service: '',
    message: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const saveContactMessage = async (data) => {
    const response = await fetch(
      "https://techyrr-server.gentlebay-8bbb7147.centralindia.azurecontainerapps.io/contact-us",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }
    );
    return response;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.name || !formData.email || !formData.service || !formData.message) {
      alert('Please fill in all required fields.');
      return;
    }

    setIsLoading(true);

    try {
      const response = await saveContactMessage({
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        company: formData.company,
        message: `Service Interest: ${formData.service}\n\n${formData.message}`,
      });

      if (response.ok) {
        alert('Thank you! Your message has been sent successfully. We\'ll get back to you soon.');
        setFormData({
          name: '',
          email: '',
          phone: '',
          company: '',
          service: '',
          message: ''
        });
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Sorry, there was an error sending your message. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const contactFeatures = [
    { icon: 'fas fa-clock', text: 'Quick Response' },
    { icon: 'fas fa-shield-alt', text: 'Quality Guaranteed' },
    { icon: 'fas fa-handshake', text: 'Transparent Process' }
  ];

  const serviceOptions = [
    'Bug Fix',
    'API Integration',
    'Feature Development',
    'Code Review',
    'Full Module Development',
    'Other'
  ];

  return (
    <section id="contact" className="contact">
      <div className="container">
        <div className="contact-content">
          <div className="contact-info">
            <h2>Ready to Get Started?</h2>
            <p>Tell us about your project and we'll match you with the perfect developer and project manager.</p>
            <div className="contact-features">
              {contactFeatures.map((feature, index) => (
                <div key={index} className="contact-feature">
                  <i className={feature.icon}></i>
                  <span>{feature.text}</span>
                </div>
              ))}
            </div>
          </div>

          <form className="contact-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="name">Full Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">Work Email *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="phone">Phone Number</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
              />
            </div>

            <div className="form-group">
              <label htmlFor="company">Company Name</label>
              <input
                type="text"
                id="company"
                name="company"
                value={formData.company}
                onChange={handleInputChange}
              />
            </div>

            <div className="form-group">
              <label htmlFor="service">Service Interest *</label>
              <select
                id="service"
                name="service"
                value={formData.service}
                onChange={handleInputChange}
                required
              >
                <option value="">Select a service</option>
                {serviceOptions.map((option, index) => (
                  <option key={index} value={option}>{option}</option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="message">Project Details *</label>
              <textarea
                id="message"
                name="message"
                rows="4"
                placeholder="Tell us about your project requirements..."
                value={formData.message}
                onChange={handleInputChange}
                required
              ></textarea>
            </div>

            <button
              type="submit"
              className={`btn-primary btn-large ${isLoading ? 'loading' : ''}`}
              disabled={isLoading}
            >
              <i className={isLoading ? 'fas fa-spinner fa-spin' : 'fas fa-paper-plane'}></i>
              {isLoading ? 'Sending...' : 'Send Message'}
            </button>
          </form>
        </div>
      </div>
    </section>
  );
};

export default Contact;
