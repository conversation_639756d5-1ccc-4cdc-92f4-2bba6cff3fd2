import React from 'react';

const Hero = () => {
  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <section className="hero">
      <div className="container">
        <div className="hero-content">
          <h1 className="hero-title">
            On-Demand Developers.<br />
            <span className="highlight">Task-Based. Transparent. Managed.</span>
          </h1>
          <p className="hero-subtitle">
            Get your tech tasks done—fast, reliably, and without the hassle of hiring.
          </p>
          <div className="hero-cta">
            <a 
              href="#contact" 
              className="btn-primary btn-large"
              onClick={(e) => {
                e.preventDefault();
                scrollToContact();
              }}
            >
              <i className="fas fa-rocket"></i>
              Talk to a Project Manager
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
