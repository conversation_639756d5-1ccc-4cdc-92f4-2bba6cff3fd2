# Azure Static Web Apps Deployment Guide

## 🚨 Troubleshooting the Current Issue

The error you're seeing suggests an issue with Azure resource provider permissions or configuration. Here are several solutions:

### Solution 1: Use Deployment Token (Recommended)

1. **Get your deployment token**:
   - Go to [Azure Portal](https://portal.azure.com)
   - Navigate to your Static Web App resource
   - Go to "Overview" → "Manage deployment token"
   - Copy the deployment token

2. **Set the environment variable**:
   ```bash
   export AZURE_STATIC_WEB_APPS_API_TOKEN="your-deployment-token-here"
   ```

3. **Run the deployment**:
   ```bash
   ./deploy.sh
   ```

### Solution 2: Manual SWA CLI Deployment

1. **Install SWA CLI globally**:
   ```bash
   npm install -g @azure/static-web-apps-cli
   ```

2. **Deploy directly**:
   ```bash
   # Build first
   npm run build
   
   # Deploy with token
   swa deploy ./dist --deployment-token "your-deployment-token-here"
   ```

### Solution 3: GitHub Actions (Automated - Best Option)

1. **Push your code to GitHub**:
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin https://github.com/yourusername/your-repo.git
   git push -u origin main
   ```

2. **Create Azure Static Web App**:
   - Go to Azure Portal
   - Create new "Static Web App"
   - Connect to your GitHub repository
   - Set build configuration:
     - App location: `/`
     - Output location: `dist`
   - Azure will automatically add the deployment token to your GitHub secrets

3. **Automatic deployment**: Every push to main branch will trigger deployment!

### Solution 4: Azure CLI Alternative

If the resource provider issue persists, try these Azure CLI commands:

```bash
# Register the Microsoft.Web provider
az provider register --namespace Microsoft.Web

# Wait for registration to complete
az provider show --namespace Microsoft.Web --query "registrationState"

# List your static web apps
az staticwebapp list --resource-group hyrr

# Get deployment token
az staticwebapp secrets list --name devondemand --resource-group hyrr
```

## 🔍 Verification Steps

After deployment, verify your app:

1. **Check Azure Portal**:
   - Go to your Static Web App resource
   - Click "Browse" to view the live site
   - Check "Functions" tab if you have API functions

2. **Test functionality**:
   - Navigate through all sections
   - Test the contact form
   - Verify responsive design on mobile

3. **Check logs**:
   - In Azure Portal, go to "Log stream" for real-time logs
   - Check "Deployment history" for build logs

## 🚀 Quick Deployment Commands

```bash
# Method 1: Using deployment token
export AZURE_STATIC_WEB_APPS_API_TOKEN="your-token"
npm run build
swa deploy ./dist --deployment-token $AZURE_STATIC_WEB_APPS_API_TOKEN

# Method 2: Using resource group (if permissions allow)
npm run build
swa deploy ./dist --resource-group hyrr --app-name devondemand --env production

# Method 3: Interactive deployment
npm run build
swa deploy ./dist
```

## 🔧 Common Issues and Solutions

### Issue: "Resource provider failed"
- **Solution**: Use deployment token instead of resource group method
- **Alternative**: Register Microsoft.Web provider: `az provider register --namespace Microsoft.Web`

### Issue: "Authentication failed"
- **Solution**: Run `az login` and ensure you have contributor access to the resource group

### Issue: "Static Web App not found"
- **Solution**: Verify the app name and resource group are correct
- **Check**: `az staticwebapp list --resource-group hyrr`

### Issue: "Build fails"
- **Solution**: Ensure Node.js version compatibility (Node 16+ recommended)
- **Check**: Local build works with `npm run build`

## 📞 Need Help?

If you continue to have issues:

1. **Check Azure Status**: Visit [Azure Status](https://status.azure.com/)
2. **Verify Permissions**: Ensure you have "Static Web App Contributor" role
3. **Use GitHub Actions**: This is the most reliable deployment method
4. **Contact Support**: Create an Azure support ticket if the issue persists

## 🎯 Recommended Approach

For the most reliable deployment:

1. **Use GitHub Actions** (set up once, works forever)
2. **Keep deployment token as backup** for manual deployments
3. **Use local preview** (`npm run preview`) for testing before deployment
